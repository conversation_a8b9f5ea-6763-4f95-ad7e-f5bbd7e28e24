{"name": "human_2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint", "postbuild": "next-sitemap", "format": "prettier --check --ignore-path .gitignore .", "format:fix": "prettier --write --ignore-path .gitignore ."}, "dependencies": {"@apollo/client": "^3.8.7", "@apollo/experimental-nextjs-app-support": "^0.10.0", "@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@formatjs/intl-localematcher": "^0.2.32", "@heroicons/react": "^2.0.16", "@hookform/error-message": "^2.0.1", "@keystone-6/document-renderer": "^1.1.2", "@material-tailwind/react": "2.1.0", "@mui/icons-material": "^5.11.11", "@react-google-maps/api": "^2.19.2", "@tailwindcss/line-clamp": "^0.4.4", "@vercel/analytics": "^0.1.11", "accept-language-parser": "^1.5.0", "date-fns": "^3.6.0", "eslint-config-next": "^13.4.0", "framer-motion": "^10.2.3", "graphql": "^16.8.1", "i18next-browser-languagedetector": "^7.0.1", "i18next-resources-to-backend": "^1.1.3", "negotiator": "^0.6.3", "next": "^14.0.1", "next-client-cookies": "^1.1.1", "next-i18next": "^13.2.2", "next-sitemap": "^4.2.3", "react": "^18.2.0", "react-cool-onclickoutside": "^1.7.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-hook-form": "^7.43.5", "react-icons": "^4.8.0", "react-multi-carousel": "^2.8.3", "react-share": "^5.0.3", "sharp": "^0.32.6", "typescript": "4.9.5", "universal-cookie": "7.1.4"}, "devDependencies": {"@svgr/webpack": "^6.5.1", "@tailwindcss/forms": "^0.5.7", "@types/accept-language-parser": "^1.5.3", "@types/negotiator": "^0.6.1", "@types/node": "18.14.6", "@types/react": "18.2.9", "@types/react-dom": "18.0.11", "@types/react-world-flags": "^1.4.2", "@typescript-eslint/eslint-plugin": "^5.59.1", "@typescript-eslint/parser": "^5.59.1", "autoprefixer": "^10.4.16", "eslint": "^8.39.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-react": "^7.32.2", "postcss": "^8.4.21", "prettier": "^2.8.7", "prettier-plugin-tailwindcss": "^0.2.4", "tailwindcss": "^3.2.7", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0"}}