const monthsRo = [
	"<PERSON><PERSON><PERSON>",
	"<PERSON><PERSON><PERSON><PERSON>",
	"<PERSON><PERSON>",
	"<PERSON><PERSON>",
	"<PERSON>",
	"<PERSON><PERSON><PERSON>",
	"<PERSON><PERSON><PERSON>",
	"August",
	"<PERSON><PERSON><PERSON><PERSON>",
	"<PERSON><PERSON><PERSON>",
	"<PERSON><PERSON><PERSON><PERSON>",
	"<PERSON><PERSON><PERSON><PERSON>",
];
const monthsIt = [
	"<PERSON><PERSON><PERSON>",
	"<PERSON><PERSON><PERSON>",
	"ma<PERSON><PERSON>",
	"<PERSON><PERSON>",
	"<PERSON><PERSON>",
	"<PERSON>iu<PERSON>",
	"<PERSON><PERSON><PERSON>",
	"A<PERSON><PERSON>",
	"Settembre",
	"Ottobre",
	"Novembre",
	"Dicembre",
];

const daysRo = ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Sam<PERSON>"];
const daysIt = ["Domenica", "Lunedi", "Mertedi", "Mercoledì", "Giovedì", "Venerdì", "Sabato"];

export default function formatDate(dateCreated: string, params: { lang: string }) {
	const newDate = new Date(dateCreated);
	if (params.lang === "ro") {
		const month = newDate.getMonth();
		const monthName = monthsRo[`${month}`];
		const day = newDate.getDay();
		const dayName = daysRo[day];
		const formattedDate = `${dayName},  ${newDate.getDate()}  ${monthName}  ${newDate.getFullYear()} `;
		return formattedDate;
	} else {
		const month = newDate.getMonth();
		const monthName = monthsIt[`${month}`];
		const day = newDate.getDay();
		const dayName = daysIt[day];
		const formattedDate = `${dayName},  ${newDate.getDate()}  ${monthName}  ${newDate.getFullYear()} `;
		return formattedDate;
	}
}
